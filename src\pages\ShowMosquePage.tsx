import React, { useState, useEffect, useCallback, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  getFirestore,
  collection,
  query,
  where,
  getDocs,
  onSnapshot,
} from "firebase/firestore";
import { Mosque } from "../models/Mosque";
import { PrayerTime } from "../models/PrayerTime";
import Tooltip from "../components/Tooltip";
import { useTheme } from "../contexts/ThemeContext";
import HijriDate from "hijri-date";
import {
  MOSQUES_COLLECTION,
  PRAYER_TIMES_COLLECTION,
} from "../constants/firebase";

// Add these utility functions at the top
const convertToHindiDigits = (num: number): string => {
  const hindiDigits = ["۰", "۱", "۲", "۳", "٤", "۵", "٦", "۷", "۸", "۹"];
  return num.toString().replace(/\d/g, (d) => hindiDigits[+d]);
};

const getHijriDate = () => {
  const hijri = new HijriDate();

  const hijriMonths: Record<number, string> = {
    1: "محرم",
    2: "صفر",
    3: "ربيع الأول",
    4: "ربيع الثاني",
    5: "جمادى الأولى",
    6: "جمادى الآخرة",
    7: "رجب",
    8: "شعبان",
    9: "رمضان",
    10: "شوال",
    11: "ذو القعدة",
    12: "ذو الحجة",
  };

  const adjustedDate = hijri.date - 1;

  if (adjustedDate === 0) {
    console.warn("Date adjustment needed for month rollover");
    return `${convertToHindiDigits(hijri.date)} ${
      hijriMonths[hijri.month as keyof typeof hijriMonths]
    } (${convertToHindiDigits(hijri.month)}) ${convertToHindiDigits(
      hijri.year
    )} هـ`;
  }

  return `${convertToHindiDigits(adjustedDate)} ${
    hijriMonths[hijri.month as keyof typeof hijriMonths]
  } (${convertToHindiDigits(hijri.month)}) ${convertToHindiDigits(
    hijri.year
  )} هـ`;
};

const getGregorianDate = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const adjustTime = (
  timeStr: string,
  minutes: number,
  isDST: boolean = false
): string => {
  const [hours, mins] = timeStr.split(":").map(Number);
  const date = new Date();

  // Convert PM times to 24-hour format
  let adjustedHours = hours;
  if (timeStr.includes("PM")) {
    adjustedHours = hours === 12 ? 12 : hours + 12;
  }

  // Add an extra hour if daylight saving is active
  const dstAdjustment = isDST ? 1 : 0;
  date.setHours(adjustedHours + dstAdjustment, mins + minutes);

  // Use 24-hour format (HH:mm)
  return date.toLocaleTimeString("he-IL", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: false,
  });
};

const timeToMinutes = (timeStr: string): number => {
  const [hours, minutes] = timeStr.split(":").map(Number);
  return hours * 60 + minutes;
};

const getCurrentTimeInMinutes = (): number => {
  const now = new Date();
  const totalMinutes = now.getHours() * 60 + now.getMinutes();
  const totalSeconds = totalMinutes * 60 + now.getSeconds();
  return totalSeconds;
};

const getPrayerStatus = (
  prayerTime: string,
  iqamaShift: number,
  adhanCountDown: number,
  prayerName: PrayerName,
  allPrayerTimes: Record<PrayerName, string>
): "approaching" | "prayer-time" | "iqama-time" | "normal" => {
  const currentMinutes = getCurrentTimeInMinutes() / 60; // Convert seconds to minutes
  const prayerMinutes = timeToMinutes(prayerTime);
  const iqamaMinutes = prayerMinutes + iqamaShift;

  // First check if we're approaching this prayer
  if (
    currentMinutes >= prayerMinutes - adhanCountDown &&
    currentMinutes < prayerMinutes
  ) {
    return "approaching";
  }

  // Convert all prayer times to minutes for comparison
  const times = {
    fajr: timeToMinutes(allPrayerTimes.fajr),
    shorouq: timeToMinutes(allPrayerTimes.shorouq),
    duhr: timeToMinutes(allPrayerTimes.duhr),
    asr: timeToMinutes(allPrayerTimes.asr),
    maghreb: timeToMinutes(allPrayerTimes.maghreb),
    eshaa: timeToMinutes(allPrayerTimes.eshaa),
  };

  // Function to check if this is the current active prayer
  const isCurrentPrayer = () => {
    const prayerOrder = ["fajr", "shorouq", "duhr", "asr", "maghreb", "eshaa"];
    const currentPrayerIndex = prayerOrder.indexOf(prayerName.toLowerCase());
    const nextPrayerName =
      prayerOrder[(currentPrayerIndex + 1) % prayerOrder.length];
    const nextPrayerTime = times[nextPrayerName as keyof typeof times];

    // Special handling for Isha prayer (active from Isha time until midnight)
    if (prayerName.toLowerCase() === "eshaa") {
      return currentMinutes >= times.eshaa && currentMinutes < 24 * 60; // Before midnight
    }

    // For all other prayers
    return currentMinutes >= prayerMinutes && currentMinutes < nextPrayerTime;
  };

  // Only proceed with active prayer status checks if this is the current prayer
  if (!isCurrentPrayer()) {
    return "normal";
  }

  // Changed: prayer-time now starts when adhan time arrives (prayerMinutes)
  // iqama-time remains when iqama time arrives
  if (currentMinutes >= prayerMinutes && currentMinutes < iqamaMinutes) {
    return "prayer-time";
  } else if (currentMinutes >= iqamaMinutes) {
    return "iqama-time";
  }

  return "normal";
};

const convertTo24Hour = (timeStr: string, prayerName?: string): string => {
  const [hoursStr, minutesStr] = timeStr.split(":");
  let hours = Number(hoursStr);
  const minutes = Number(minutesStr);

  // Only convert afternoon/evening prayers to 24-hour format
  if (
    prayerName === "asr" ||
    prayerName === "maghreb" ||
    prayerName === "eshaa"
  ) {
    hours += 12;
  }

  return `${hours.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}`;
};

type PrayerName = "fajr" | "shorouq" | "duhr" | "asr" | "maghreb" | "eshaa";

const formatTimeRemaining = (totalSeconds: number): string => {
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  return `${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")}`;
};

// Function to make numeric digits shiny in countdown text
const makeDigitsShiny = (text: string): React.ReactElement => {
  // Split the text by digits and colons to identify numeric parts
  const parts = text.split(/(\d+:\d+)/);

  return (
    <>
      {parts.map((part, index) => {
        // Check if this part contains digits and colon (time format)
        if (/\d+:\d+/.test(part)) {
          return (
            <span
              key={index}
              className="inline-block bg-gradient-to-r from-yellow-300 via-yellow-400 to-yellow-500 dark:from-yellow-400 dark:via-yellow-300 dark:to-yellow-200 bg-clip-text text-transparent font-black"
              style={{
                filter: "brightness(1.2) contrast(1.1)",
              }}
            >
              {part}
            </span>
          );
        }
        return <span key={index}>{part}</span>;
      })}
    </>
  );
};

const TawqitRegionalDisplay = ({
  prayerTimes,
  mosque,
}: {
  prayerTimes: PrayerTime | null;
  mosque: Mosque | null;
}) => {
  const [countdown, setCountdown] = useState<string>("");
  const [countdownType, setCountdownType] = useState<
    "adhan" | "iqama" | "none"
  >("none");

  useEffect(() => {
    const calculateCountdown = () => {
      if (!prayerTimes || !mosque) return;

      const now = new Date();
      const currentMinutes = now.getHours() * 60 + now.getMinutes();
      // const currentSeconds = currentMinutes * 60 + now.getSeconds();
      const prayerOrder: PrayerName[] = [
        "fajr",
        "shorouq",
        "duhr",
        "asr",
        "maghreb",
        "eshaa",
      ];

      for (const prayer of prayerOrder) {
        const prayerTime = timeToMinutes(prayerTimes[prayer]);
        const prayerTimeInSeconds = prayerTime * 60;
        const iqamaTime =
          prayerTime +
          ((mosque[
            `iqama${
              prayer.charAt(0).toUpperCase() + prayer.slice(1)
            }Shift` as keyof Mosque
          ] as number) || 0);
        const iqamaTimeInSeconds = iqamaTime * 60;

        // Check for Adhan countdown
        if (
          currentMinutes >= prayerTime - (mosque.adhanCountDown || 30) &&
          currentMinutes < prayerTime
        ) {
          const remainingSeconds =
            prayerTimeInSeconds - (currentMinutes * 60 + now.getSeconds());
          if (prayer === "shorouq") {
            setCountdown(
              `باقي للشروق ${formatTimeRemaining(remainingSeconds)} د'`
            );
            console.log(remainingSeconds);
          } else {
            setCountdown(
              `باقي للأذان ${formatTimeRemaining(remainingSeconds)} د'`
            );
          }
          setCountdownType("adhan");
          return;
        }

        // Check for Iqama countdown
        if (currentMinutes >= prayerTime && currentMinutes < iqamaTime) {
          const remainingSeconds =
            iqamaTimeInSeconds - (currentMinutes * 60 + now.getSeconds());
          setCountdown(
            `باقي للإقامة ${formatTimeRemaining(remainingSeconds)} د'`
          );
          setCountdownType("iqama");
          return;
        }
      }

      setCountdown("");
      setCountdownType("none");
    };

    const timer = setInterval(calculateCountdown, 1000);
    return () => clearInterval(timer);
  }, [prayerTimes, mosque]);

  // Show countdown if there's an active countdown, otherwise show tawqit regional info
  if (countdownType !== "none") {
    return (
      <div
        className={`text-center mt-2 mb-4 text-lg md:text-xl lg:text-2xl font-bold ${
          countdownType === "adhan"
            ? "text-yellow-600 dark:text-yellow-400"
            : "text-blue-600 dark:text-blue-400"
        }`}
      >
        {makeDigitsShiny(countdown)}
      </div>
    );
  }

  // Show tawqit regional and reference city info when no countdown is active
  return (
    <div className="text-xs md:text-sm lg:text-xl xl:text-xl font-black text-gray-800 dark:text-yellow-200 text-center flex items-center justify-center leading-none mt-2 mb-4">
      {mosque
        ? "توقيت " +
          mosque.regionalCity +
          " ( " +
          mosque.referenceCity +
          " " +
          (mosque.dahriShift > 0 ? "+" : "") +
          (mosque.dahriShift != 0 ? mosque.dahriShift + "د'" : "") +
          " ) "
        : "..."}
    </div>
  );
};

export default function ShowMosquePage() {
  const { friendlyUrlName } = useParams();
  const navigate = useNavigate();
  const { isDarkMode, toggleDarkMode } = useTheme();
  const [mosque, setMosque] = useState<Mosque | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [currentTime, setCurrentTime] = useState(new Date());
  const [currentSlideIndex, setCurrentSlideIndex] = useState(0);
  const [showMenu, setShowMenu] = useState(false);
  const [prayerTimes, setPrayerTimes] = useState<PrayerTime | null>(null);
  const [lastFetchDate, setLastFetchDate] = useState(new Date());
  const [marqueePosition, setMarqueePosition] = useState(-100);
  const containerRef = useRef<HTMLDivElement>(null);
  const marqueeRef = useRef<HTMLDivElement>(null);
  const menuRef = useRef<HTMLDivElement>(null);

  // Handle fullscreen
  const toggleFullScreen = () => {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen();
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };

  // Handle page reload
  const handleReload = () => {
    window.location.reload();
    setShowMenu(false); // Close menu after reload
  };

  // Handle click outside menu to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setShowMenu(false);
      }
    };

    if (showMenu) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [showMenu]);

  // Clock update effect
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Slide rotation effect
  useEffect(() => {
    if (!mosque?.sliderItems?.length) return;

    const slideInterval = setInterval(() => {
      setCurrentSlideIndex((current) =>
        current === mosque.sliderItems.length - 1 ? 0 : current + 1
      );
    }, (mosque.sliderDelayTime || 5) * 1000);

    return () => clearInterval(slideInterval);
  }, [mosque?.sliderItems, mosque?.sliderDelayTime]);

  useEffect(() => {
    console.log("Starting to listen to mosque data...");
    setLoading(true);

    const db = getFirestore();
    const mosquesRef = collection(db, MOSQUES_COLLECTION);
    const q = query(
      mosquesRef,
      where("friendlyUrlName", "==", friendlyUrlName)
    );

    // Set up real-time listener
    const unsubscribe = onSnapshot(
      q,
      (querySnapshot) => {
        if (querySnapshot.empty) {
          console.log("No mosque found with this friendlyUrlName");
          setError("Mosque not found");
          setLoading(false);
          return;
        }

        const mosqueData = querySnapshot.docs[0].data() as Mosque;
        console.log("Mosque data updated:", mosqueData);
        setMosque(mosqueData);
        document.title = `${mosqueData.name} - ${mosqueData.city}`;
        setLoading(false);
      },
      (error) => {
        console.error("Error listening to mosque data:", error);
        setError("Failed to load mosque data");
        setLoading(false);
      }
    );

    // Cleanup listener on component unmount
    return () => {
      unsubscribe();
      document.title = "المساجد";
    };
  }, [friendlyUrlName]);

  const fetchPrayerTimes = useCallback(async () => {
    if (!mosque) {
      console.log("No mosque data available");
      return;
    }

    try {
      const today = new Date();
      const dayOfMonth = today.getDate().toString();
      const monthNumber = (today.getMonth() + 1).toString();

      console.log("Fetching prayer times with params:", {
        dayOfMonth,
        monthNumber,
        timeZone: mosque.timeZone,
        isDST: mosque.dayLightSaving,
      });

      const db = getFirestore();
      const prayerTimesRef = collection(db, PRAYER_TIMES_COLLECTION);
      const q = query(
        prayerTimesRef,
        where("dayOfMonth", "==", dayOfMonth),
        where("monthNumber", "==", monthNumber),
        where("timeZone", "==", mosque.timeZone)
      );

      const querySnapshot = await getDocs(q);

      if (!querySnapshot.empty) {
        const prayerTimeData = querySnapshot.docs[0].data() as PrayerTime;
        const dahriShift = Number(mosque.dahriShift) || 0;
        const isDST = mosque.dayLightSaving || false;

        const adjustedTimes = {
          ...prayerTimeData,
          fajr: adjustTime(
            convertTo24Hour(prayerTimeData.fajr),
            dahriShift,
            isDST
          ),
          shorouq: adjustTime(
            convertTo24Hour(prayerTimeData.shorouq),
            dahriShift,
            isDST
          ),
          duhr: adjustTime(
            convertTo24Hour(prayerTimeData.duhr),
            dahriShift,
            isDST
          ),
          asr: adjustTime(
            convertTo24Hour(prayerTimeData.asr, "asr"),
            dahriShift,
            isDST
          ),
          maghreb: adjustTime(
            convertTo24Hour(prayerTimeData.maghreb, "maghreb"),
            dahriShift,
            isDST
          ),
          eshaa: adjustTime(
            convertTo24Hour(prayerTimeData.eshaa, "eshaa"),
            dahriShift,
            isDST
          ),
        };

        console.log("Adjusted prayer times (including DST):", adjustedTimes);
        setPrayerTimes(adjustedTimes);
      } else {
        console.log("No prayer times found for the new day");
      }
    } catch (err) {
      console.error("Error fetching prayer times:", err);
    }
  }, [mosque]);

  useEffect(() => {
    if (mosque && mosque.timeZone) {
      fetchPrayerTimes();
    }
  }, [mosque, fetchPrayerTimes]);

  // Add this function to check if we need to fetch new times
  const shouldFetchNewTimes = (currentDate: Date, lastFetchDate: Date) => {
    return (
      currentDate.getDate() !== lastFetchDate.getDate() ||
      currentDate.getMonth() !== lastFetchDate.getMonth()
    );
  };

  useEffect(() => {
    const timer = setInterval(() => {
      const now = new Date();

      // Check if we need to fetch new prayer times
      if (shouldFetchNewTimes(now, lastFetchDate)) {
        fetchPrayerTimes();
        setLastFetchDate(now);
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [lastFetchDate, fetchPrayerTimes]);

  // JavaScript-based marquee animation
  useEffect(() => {
    if (!mosque?.news) return;

    let animationId: number;
    let cycleStartTime: number;
    let textWidth: number = 0;
    let containerWidth: number = 0;
    let scrollDuration: number = 0;
    const constantSpeed = 80; // pixels per second - constant speed for readability
    const pauseDuration = 5000; // 5 seconds pause between cycles

    const calculateDimensions = () => {
      if (marqueeRef.current && containerRef.current) {
        const marquee = marqueeRef.current;
        const container = containerRef.current;
        textWidth = marquee.scrollWidth;
        containerWidth = container.offsetWidth;

        // Calculate duration based on constant speed
        // Distance = text width + container width (for complete disappearance)
        const scrollDistance = textWidth + containerWidth;
        scrollDuration = (scrollDistance / constantSpeed) * 1000; // Convert to milliseconds
      }
    };

    const animateMarquee = (timestamp: number) => {
      if (!cycleStartTime) {
        cycleStartTime = timestamp;
        calculateDimensions();
      }

      const cycleElapsed = timestamp - cycleStartTime;
      const totalCycleDuration = pauseDuration + scrollDuration;

      // 5 second pause at the start of each cycle
      if (cycleElapsed < pauseDuration) {
        // Position text completely off-screen to the right (for RTL scrolling)
        setMarqueePosition(100);
        animationId = requestAnimationFrame(animateMarquee);
        return;
      }

      // Animation phase
      const animationTime = cycleElapsed - pauseDuration;

      if (animationTime < scrollDuration) {
        const progress = animationTime / scrollDuration;

        // RTL: Start position: text completely off-screen to the right
        // RTL: End position: text completely off-screen to the left
        const startPos = 100; // Completely off-screen to the right
        const endPos = -(textWidth / containerWidth) * 100; // Completely off-screen to the left

        const position = startPos + progress * (endPos - startPos);
        setMarqueePosition(position);

        animationId = requestAnimationFrame(animateMarquee);
      } else {
        // Animation completed, wait for next cycle
        if (cycleElapsed >= totalCycleDuration) {
          cycleStartTime = timestamp; // Reset for next cycle
        }
        animationId = requestAnimationFrame(animateMarquee);
      }
    };

    // Start animation
    animationId = requestAnimationFrame(animateMarquee);

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, [mosque?.news]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-900">
        <div className="text-xl text-gray-800 dark:text-white">
          جاري التحميل...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white dark:bg-gray-900">
        <div className="text-xl text-red-600 dark:text-red-400">{error}</div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col bg-blue-500 dark:bg-blue-900">
      {/* Top Bar */}
      <div className="h-12 md:h-16">
        <div className="h-full px-3 md:px-6 flex items-center justify-between">
          {/* Right Section - Menu and Dates */}
          <div className="flex items-center gap-2 md:gap-6">
            {/* Hamburger Menu */}
            <div className="relative" ref={menuRef}>
              <button
                onClick={() => setShowMenu(!showMenu)}
                className="text-white hover:bg-green-500 p-0.5 md:p-1.5 rounded-lg"
              >
                <svg
                  className="w-4 h-4 md:w-5 md:h-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>

              {/* Dropdown Menu */}
              {showMenu && (
                <div className="absolute top-full right-0 mt-2 w-40 md:w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg z-50">
                  <button
                    onClick={() => navigate("/signin")}
                    className="w-full text-right px-3 md:px-4 py-2 text-sm md:text-base text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <svg
                      className="w-4 h-4 md:w-5 md:h-5 text-gray-700 dark:text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                      />
                    </svg>
                    تعديل
                  </button>

                  <button
                    onClick={handleReload}
                    className="w-full text-right px-3 md:px-4 py-2 text-sm md:text-base text-gray-700 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <svg
                      className="w-4 h-4 md:w-5 md:h-5 text-gray-700 dark:text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                      />
                    </svg>
                    تحديث
                  </button>
                </div>
              )}
            </div>

                        {/* Fullscreen Toggle */}
            <Tooltip text="ملء الشاشة">
              <button
                onClick={toggleFullScreen}
                className="text-white hover:bg-green-500 p-0.5 md:p-1.5 rounded-lg"
              >
                <svg
                  className="w-4 h-4 md:w-5 md:h-5 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
                  />
                </svg>
              </button>
            </Tooltip>

            {/* Theme Toggle */}
            <Tooltip text={isDarkMode ? "وضع النهار" : "وضع الليل"}>
              <button
                onClick={toggleDarkMode}
                className="text-white hover:bg-green-500 p-0.5 md:p-1.5 rounded-lg"
              >
                {isDarkMode ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 md:h-5 md:w-5 text-yellow-300"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-4 w-4 md:h-5 md:w-5 text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"
                    />
                  </svg>
                )}
              </button>
            </Tooltip>

            {/* Dates */}
            <div className="text-white text-sm md:text-3xl flex items-center">
              <span dir="rtl" className="font-bold text-yellow-300">
                {getHijriDate()}
              </span>
              <span className="mr-2 md:mr-4 text-gray-200" dir="ltr">
                {getGregorianDate()}
              </span>
            </div>
          </div>

          {/* Left Section - Time */}
          <div className="text-2xl md:text-5xl font-black text-yellow-300 digital-clock w-28 md:w-[240px] text-left">
            {currentTime.toLocaleTimeString("he-IL", {
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
              hour12: false,
            })}
          </div>
        </div>
      </div>

      {/* Main Content Area - Responsive Layout */}
      <div
        className={`flex-1 flex flex-col md:flex-row ${
          mosque?.showNews
            ? "h-[calc(100vh-8.5rem)] md:h-[calc(100vh-8rem)] lg:h-[calc(100vh-13rem)]"
            : "h-[calc(100vh-3rem)] md:h-[calc(100vh-4rem)] lg:h-[calc(100vh-8rem)]"
        }`}
      >
        {/* Prayer Times Section - First on mobile, left side on desktop */}
        {mosque?.showPrayTimes && (
          <div className="w-full md:w-[30%] p-1 md:p-2 md:order-2 md:h-full">
            <div className="h-full border-2 border-gray-600 dark:border-gray-300 rounded-lg p-1 md:p-2 py-0 overflow-y-auto scrollbar-hide bg-gray-200 dark:bg-gray-800">
              {/* Center - Mosque Name */}
              <div className="text-sm md:text-lg lg:text-2xl xl:text-3xl font-black text-gray-800 dark:text-white text-center flex items-center justify-center leading-none py-1 md:py-2 lg:pb-1 pt-0">
                <span>
                  {mosque?.name ? `${mosque.name} - ${mosque.city}` : "المسجد"}
                </span>
              </div>
              <TawqitRegionalDisplay
                prayerTimes={prayerTimes}
                mosque={mosque}
              />
              {/* Prayer Times Header */}
              <div className="grid grid-cols-3 gap-1 md:gap-2 lg:gap-3 items-center p-1 md:p-1.5 lg:p-2 rounded-lg bg-gray-100 dark:bg-gray-600 mb-1 md:mb-2">
                <div className="text-xs md:text-sm lg:text-lg xl:text-xl font-bold text-gray-700 dark:text-gray-200 text-right">
                  الصلاة
                </div>
                <div className="text-xs md:text-sm lg:text-lg xl:text-xl font-bold text-gray-700 dark:text-gray-200 text-right">
                  الأذان
                </div>
                <div className="text-xs md:text-sm lg:text-lg xl:text-xl font-bold text-gray-700 dark:text-gray-200 text-right">
                  الإقامة
                </div>
              </div>
              <div className="space-y-1 md:space-y-1 lg:space-y-2">
                <PrayerTimeRow
                  name="الفجر"
                  time={prayerTimes?.fajr ?? "--:--"}
                  iqamaShift={mosque?.iqamaFajrShift || 0}
                  adhanCountDown={mosque?.adhanCountDown || 30}
                  prayerTimes={
                    prayerTimes ?? {
                      fajr: "--:--",
                      shorouq: "--:--",
                      duhr: "--:--",
                      asr: "--:--",
                      maghreb: "--:--",
                      eshaa: "--:--",
                    }
                  }
                />
                <PrayerTimeRow
                  name="الشروق"
                  time={prayerTimes?.shorouq ?? "--:--"}
                  iqamaShift={mosque?.iqamaShorouqShift || 0}
                  adhanCountDown={mosque?.adhanCountDown || 30}
                  prayerTimes={
                    prayerTimes ?? {
                      fajr: "--:--",
                      shorouq: "--:--",
                      duhr: "--:--",
                      asr: "--:--",
                      maghreb: "--:--",
                      eshaa: "--:--",
                    }
                  }
                />
                <PrayerTimeRow
                  name="الظهر"
                  time={prayerTimes?.duhr ?? "--:--"}
                  iqamaShift={mosque?.iqamaDuhrShift || 0}
                  adhanCountDown={mosque?.adhanCountDown || 30}
                  prayerTimes={
                    prayerTimes ?? {
                      fajr: "--:--",
                      shorouq: "--:--",
                      duhr: "--:--",
                      asr: "--:--",
                      maghreb: "--:--",
                      eshaa: "--:--",
                    }
                  }
                />
                <PrayerTimeRow
                  name="العصر"
                  time={prayerTimes?.asr ?? "--:--"}
                  iqamaShift={mosque?.iqamaAsrShift || 0}
                  adhanCountDown={mosque?.adhanCountDown || 30}
                  prayerTimes={
                    prayerTimes ?? {
                      fajr: "--:--",
                      shorouq: "--:--",
                      duhr: "--:--",
                      asr: "--:--",
                      maghreb: "--:--",
                      eshaa: "--:--",
                    }
                  }
                />
                <PrayerTimeRow
                  name="المغرب"
                  time={prayerTimes?.maghreb ?? "--:--"}
                  iqamaShift={mosque?.iqamaMaghrebShift || 0}
                  adhanCountDown={mosque?.adhanCountDown || 30}
                  prayerTimes={
                    prayerTimes ?? {
                      fajr: "--:--",
                      shorouq: "--:--",
                      duhr: "--:--",
                      asr: "--:--",
                      maghreb: "--:--",
                      eshaa: "--:--",
                    }
                  }
                />
                <PrayerTimeRow
                  name="العشاء"
                  time={prayerTimes?.eshaa ?? "--:--"}
                  iqamaShift={mosque?.iqamaEshaaShift || 0}
                  adhanCountDown={mosque?.adhanCountDown || 30}
                  prayerTimes={
                    prayerTimes ?? {
                      fajr: "--:--",
                      shorouq: "--:--",
                      duhr: "--:--",
                      asr: "--:--",
                      maghreb: "--:--",
                      eshaa: "--:--",
                    }
                  }
                />
              </div>
            </div>
          </div>
        )}

        {/* Mobile Slides Section - Only visible on mobile */}
        {mosque?.sliderItems && mosque.sliderItems.length > 0 && (
          <div className="flex-1 md:hidden p-2">
            <div className="h-full max-h-80 border-2 border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden relative bg-gray-100 dark:bg-gray-800">
              {mosque.sliderItems.map((slide, index) => (
                <div
                  key={index}
                  className={`absolute inset-0 transition-opacity duration-1000
                    ${
                      index === currentSlideIndex ? "opacity-100" : "opacity-0"
                    }`}
                >
                  <img
                    src={slide.imageUrl}
                    alt={slide.text}
                    className="w-full h-full object-contain"
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Desktop Slides Section - Only visible on desktop */}
        <div
          className={`hidden md:flex ${
            mosque?.showPrayTimes ? "md:w-[70%]" : "w-full"
          } md:order-1 py-2 pr-2`}
        >
          <div className="w-full h-full border-2 border-gray-500 dark:border-gray-200 rounded-lg overflow-hidden relative">
            {mosque?.sliderItems?.map((slide, index) => (
              <div
                key={index}
                className={`absolute inset-0 transition-opacity duration-1000
                  ${index === currentSlideIndex ? "opacity-100" : "opacity-0"}`}
              >
                <img
                  src={slide.imageUrl}
                  alt={slide.text}
                  className="w-full h-full object-fill"
                />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Footer Bar with Scrolling News - Only visible if showNews is true */}
      {mosque?.showNews && (
        <div className="h-10 md:h-16 lg:h-20 shadow-lg flex-shrink-0">
          <div
            ref={containerRef}
            className="h-full w-full relative overflow-hidden"
          >
            <div
              ref={marqueeRef}
              className="absolute whitespace-nowrap h-full flex items-center"
              style={{
                right: `${marqueePosition}%`,
              }}
            >
              <span className="text-sm md:text-xl lg:text-3xl text-white font-semibold px-2 md:px-4 lg:px-8">
                {mosque?.news || "أهلاً وسهلاً بكم في المسجد"}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

const PrayerTimeRow = ({
  name,
  time,
  iqamaShift,
  adhanCountDown,
  prayerTimes,
}: {
  name: string;
  time: string;
  iqamaShift: number;
  adhanCountDown: number;
  prayerTimes: {
    fajr: string;
    shorouq: string;
    duhr: string;
    asr: string;
    maghreb: string;
    eshaa: string;
  };
}) => {
  const getPrayerNameFromArabic = (arabicName: string): PrayerName => {
    const nameMap: Record<string, PrayerName> = {
      الفجر: "fajr",
      الشروق: "shorouq",
      الظهر: "duhr",
      العصر: "asr",
      المغرب: "maghreb",
      العشاء: "eshaa",
    };
    return nameMap[arabicName] || "fajr";
  };

  const status = getPrayerStatus(
    time,
    iqamaShift,
    adhanCountDown,
    getPrayerNameFromArabic(name),
    prayerTimes
  );

  const baseClasses =
    "grid grid-cols-3 gap-1 md:gap-2 lg:gap-3 items-center p-1 md:p-1.5 lg:p-2 rounded-lg";
  const statusClasses = {
    normal: "bg-gray-100 dark:bg-gray-700", // Changed from "bg-gray-50" to "bg-gray-100"
    approaching: "bg-gray-100 dark:bg-gray-700", // Keep original background for approaching
    "prayer-time": "bg-blue-200 dark:bg-blue-900", // Active background when adhan time arrives
    "iqama-time": "bg-blue-200 dark:bg-blue-900", // Keep active background during iqama time
  };

  const isShorouq = name === "الشروق";

  const titleClasses = `text-xs md:text-sm lg:text-lg xl:text-2xl font-bold text-right ${
    isShorouq
      ? "text-orange-600 dark:text-orange-400 italic" // Special styling for Shorouq
      : status === "approaching"
      ? "text-gray-800 dark:text-white animate-blink" // Blink during approaching
      : status === "normal"
      ? "text-gray-800 dark:text-white"
      : "text-blue-800 dark:text-blue-200" // Active text color for prayer-time and iqama-time
  }`;

  // const timeClasses = `text-sm md:text-lg lg:text-2xl xl:text-3xl font-black digital-clock text-right ${
  const timeClasses = `text-xl md:text-2xl lg:text-3xl xl:text-4xl font-black digital-clock text-right ${
    isShorouq
      ? "text-orange-600 dark:text-orange-400" // Special styling for Shorouq
      : status === "approaching"
      ? "text-gray-600 dark:text-gray-300 animate-blink" // Blink during approaching
      : status === "normal"
      ? "text-gray-600 dark:text-gray-300"
      : "text-blue-600 dark:text-blue-300" // Active text color for prayer-time and iqama-time
  }`;

  const iqamaClasses = `text-xl md:text-2xl lg:text-3xl xl:text-4xl font-black digital-clock text-right ${
    isShorouq
      ? "text-orange-500 dark:text-orange-300" // Special styling for Shorouq iqama
      : status === "prayer-time"
      ? "text-green-600 dark:text-green-300 animate-blink" // Only iqama time blinks during prayer-time
      : status === "iqama-time"
      ? "text-blue-600 dark:text-blue-300"
      : "text-green-600 dark:text-green-400"
  }`;

  return (
    <div className={`${baseClasses} ${statusClasses[status]}`}>
      <div className={titleClasses}>{name}</div>
      <div className={timeClasses}>{time || "--:--"}</div>
      <div className={iqamaClasses}>
        {time ? adjustTime(time, iqamaShift) : "--:--"}
      </div>
    </div>
  );
};
